import React, { useState, useEffect } from "react";
import { CharacterInfo } from "./CharacterInfo";
import { MovistarInfo } from "./MovistarInfo";
import { MovistarOttService } from "../services/MovistarOttService";

interface GameEndScreenProps {
  gameWon: boolean;
  onClose: () => void;
  onNewGame: () => void;
  characterName?: string;
  characterImage?: string; // Agregar imagen del personaje
}

export const GameEndScreen: React.FC<GameEndScreenProps> = ({
  gameWon,
  onClose,
  onNewGame,
  characterName = "Personaje desconocido",
  characterImage,
}) => {
  const [showCharacterInfo, setShowCharacterInfo] = useState(false);
  const [showMovistarInfo, setShowMovistarInfo] = useState(false);
  const [movistarContent, setMovistarContent] = useState<string>("");
  const [loadingMovistar, setLoadingMovistar] = useState(false);
  const [firstMovieImage, setFirstMovieImage] = useState<string>("");
  const [firstMovieTitle, setFirstMovieTitle] = useState<string>("");

  // Cargar contenido de Movistar+ cuando se monta el componente
  useEffect(() => {
    if (characterName && characterName !== "Personaje desconocido") {
      loadMovistarContent();
    }
  }, [characterName]);

  // Función para extraer la primera imagen y título del contenido HTML
  const extractFirstMovieData = (htmlContent: string) => {
    // Extraer primera imagen
    const imgMatch = htmlContent.match(/!\[([^\]]*)\]\(([^)]+)\)/);
    if (imgMatch) {
      setFirstMovieImage(imgMatch[2]);
      setFirstMovieTitle(imgMatch[1]);
    }

    // Si no hay imagen con markdown, buscar en HTML
    if (!imgMatch) {
      const htmlImgMatch = htmlContent.match(/<img[^>]+src="([^"]+)"[^>]*alt="([^"]*)"[^>]*>/);
      if (htmlImgMatch) {
        setFirstMovieImage(htmlImgMatch[1]);
        setFirstMovieTitle(htmlImgMatch[2]);
      }
    }

    // Extraer primer título en negrita si no tenemos título
    if (!firstMovieTitle) {
      const titleMatch = htmlContent.match(/<strong>([^<]+)<\/strong>/);
      if (titleMatch) {
        setFirstMovieTitle(titleMatch[1]);
      }
    }
  };

  const loadMovistarContent = async () => {
    setLoadingMovistar(true);
    try {
      const movistarService = new MovistarOttService();
      await movistarService.init();
      const result = await movistarService.sendToAgentFinalOnly(
        `Busca contenido relacionado con ${characterName} en Movistar+. Incluye título, imagen, sinopsis y clasificación si está disponible.`
      );
      setMovistarContent(result);
      extractFirstMovieData(result);
    } catch (error) {
      console.error("Error cargando contenido de Movistar+:", error);
      setMovistarContent("No se pudo cargar el contenido de Movistar+");
    } finally {
      setLoadingMovistar(false);
    }
  };
  return (
    <>
      <style>
        {`
          @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
          }
        `}
      </style>
      <div
        style={{
          position: "fixed",
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          background: "linear-gradient(135deg, #0f172a 0%, #1e293b 50%, #334155 100%)",
          display: "flex",
          justifyContent: "center",
          alignItems: "center",
          zIndex: 1000,
          padding: "20px",
        }}
      >
      <div
        style={{
          display: "flex",
          gap: "40px",
          maxWidth: "1200px",
          width: "100%",
          alignItems: "center",
        }}
      >
        {/* Sección principal izquierda */}
        <div style={{ flex: 1, textAlign: "center", color: "white" }}>
          {/* Círculo con imagen del personaje */}
          <div
            style={{
              width: "200px",
              height: "200px",
              borderRadius: "50%",
              border: gameWon ? "4px solid #10b981" : "4px solid #ef4444",
              margin: "0 auto 32px auto",
              overflow: "hidden",
              backgroundColor: "#374151",
              display: "flex",
              alignItems: "center",
              justifyContent: "center",
            }}
          >
            {characterImage ? (
              <img
                src={characterImage}
                alt={characterName}
                style={{
                  width: "100%",
                  height: "100%",
                  objectFit: "cover",
                }}
              />
            ) : (
              <div style={{ fontSize: "80px", color: "#9ca3af" }}>
                {gameWon ? "🎉" : "😔"}
              </div>
            )}
          </div>

          {/* Título */}
          <h1
            style={{
              fontSize: "48px",
              fontWeight: "bold",
              color: gameWon ? "#10b981" : "#ef4444",
              margin: "0 0 16px 0",
              textShadow: "0 2px 4px rgba(0, 0, 0, 0.3)",
            }}
          >
            {gameWon ? "¡Has acertado!" : "Buen intento"}
          </h1>

          {/* Mensaje */}
          <p
            style={{
              fontSize: "20px",
              color: "#e2e8f0",
              margin: "0 0 32px 0",
              lineHeight: "1.5",
            }}
          >
            {gameWon
              ? `El personaje es ${characterName}`
              : `El personaje es ${characterName}, pero no estabas tan lejos... ¿Quieres probar suerte con otro misterio?`}
          </p>

          {/* Botón Volver a jugar */}
          <button
            onClick={onNewGame}
            style={{
              backgroundColor: "#10b981",
              color: "white",
              border: "none",
              borderRadius: "12px",
              padding: "16px 32px",
              fontSize: "18px",
              fontWeight: "600",
              cursor: "pointer",
              transition: "all 0.2s ease",
              boxShadow: "0 4px 12px rgba(16, 185, 129, 0.3)",
            }}
            onMouseEnter={(e) => {
              e.currentTarget.style.backgroundColor = "#059669";
              e.currentTarget.style.transform = "translateY(-2px)";
              e.currentTarget.style.boxShadow = "0 6px 16px rgba(16, 185, 129, 0.4)";
            }}
            onMouseLeave={(e) => {
              e.currentTarget.style.backgroundColor = "#10b981";
              e.currentTarget.style.transform = "translateY(0)";
              e.currentTarget.style.boxShadow = "0 4px 12px rgba(16, 185, 129, 0.3)";
            }}
          >
            Volver a jugar
          </button>
        </div>

        {/* Sección derecha con tarjetas promocionales */}
        <div style={{ width: "320px", display: "flex", flexDirection: "column", gap: "20px" }}>
          {/* Tarjeta Movistar+ rectangular con imagen */}
          <div
            style={{
              backgroundColor: "rgba(255, 255, 255, 0.1)",
              backdropFilter: "blur(10px)",
              borderRadius: "16px",
              border: "1px solid rgba(255, 255, 255, 0.2)",
              color: "white",
              overflow: "hidden",
              height: "280px",
            }}
          >
            {/* Header con logo */}
            <div style={{ padding: "16px 20px", borderBottom: "1px solid rgba(255, 255, 255, 0.1)" }}>
              <div style={{ display: "flex", alignItems: "center", gap: "8px" }}>
                <div
                  style={{
                    width: "32px",
                    height: "32px",
                    backgroundColor: "#0066cc",
                    borderRadius: "8px",
                    display: "flex",
                    alignItems: "center",
                    justifyContent: "center",
                    fontSize: "16px",
                  }}
                >
                  📺
                </div>
                <h3 style={{ fontSize: "16px", fontWeight: "bold", margin: 0 }}>
                  ¿Te apetece ver contenido con {characterName}?
                </h3>
              </div>
            </div>

            {/* Contenido principal */}
            <div style={{ height: "calc(100% - 65px)", display: "flex" }}>
              {loadingMovistar ? (
                <div style={{
                  display: "flex",
                  alignItems: "center",
                  justifyContent: "center",
                  width: "100%",
                  flexDirection: "column",
                  gap: "12px"
                }}>
                  <div
                    style={{
                      width: "30px",
                      height: "30px",
                      border: "3px solid rgba(255, 255, 255, 0.3)",
                      borderTop: "3px solid white",
                      borderRadius: "50%",
                      animation: "spin 1s linear infinite",
                    }}
                  />
                  <p style={{ fontSize: "14px", color: "#cbd5e1", margin: 0 }}>Cargando contenido...</p>
                </div>
              ) : firstMovieImage ? (
                <>
                  {/* Imagen del contenido */}
                  <div style={{ width: "120px", flexShrink: 0 }}>
                    <img
                      src={firstMovieImage}
                      alt={firstMovieTitle}
                      style={{
                        width: "100%",
                        height: "100%",
                        objectFit: "cover",
                      }}
                      onError={(e) => {
                        e.currentTarget.style.display = "none";
                      }}
                    />
                  </div>

                  {/* Información del contenido */}
                  <div style={{
                    flex: 1,
                    padding: "16px 20px",
                    display: "flex",
                    flexDirection: "column",
                    justifyContent: "space-between"
                  }}>
                    <div>
                      <h4 style={{
                        fontSize: "16px",
                        fontWeight: "bold",
                        margin: "0 0 8px 0",
                        color: "white"
                      }}>
                        {firstMovieTitle}
                      </h4>
                      <p style={{
                        fontSize: "13px",
                        color: "#cbd5e1",
                        margin: "0 0 12px 0",
                        lineHeight: "1.4",
                        overflow: "hidden",
                        display: "-webkit-box",
                        WebkitLineClamp: 3,
                        WebkitBoxOrient: "vertical"
                      }}>
                        {movistarContent.replace(/<[^>]*>/g, '').substring(0, 120)}...
                      </p>
                    </div>

                    <button
                      onClick={() => setShowMovistarInfo(true)}
                      style={{
                        backgroundColor: "#0066cc",
                        color: "white",
                        padding: "8px 16px",
                        borderRadius: "8px",
                        fontSize: "13px",
                        fontWeight: "600",
                        border: "none",
                        cursor: "pointer",
                        transition: "background-color 0.2s ease",
                        alignSelf: "flex-start"
                      }}
                      onMouseEnter={(e) => {
                        e.currentTarget.style.backgroundColor = "#0052a3";
                      }}
                      onMouseLeave={(e) => {
                        e.currentTarget.style.backgroundColor = "#0066cc";
                      }}
                    >
                      Comenzar a ver
                    </button>
                  </div>
                </>
              ) : (
                /* Fallback cuando no hay imagen */
                <div style={{
                  padding: "20px",
                  display: "flex",
                  flexDirection: "column",
                  justifyContent: "center",
                  alignItems: "center",
                  textAlign: "center",
                  width: "100%"
                }}>
                  <p style={{ fontSize: "14px", color: "#cbd5e1", margin: "0 0 16px 0" }}>
                    Mira la serie "Los años nuevos"
                  </p>
                  <button
                    onClick={() => setShowMovistarInfo(true)}
                    style={{
                      backgroundColor: "#0066cc",
                      color: "white",
                      padding: "10px 20px",
                      borderRadius: "8px",
                      fontSize: "14px",
                      fontWeight: "600",
                      border: "none",
                      cursor: "pointer",
                      transition: "background-color 0.2s ease",
                    }}
                    onMouseEnter={(e) => {
                      e.currentTarget.style.backgroundColor = "#0052a3";
                    }}
                    onMouseLeave={(e) => {
                      e.currentTarget.style.backgroundColor = "#0066cc";
                    }}
                  >
                    Comenzar a ver
                  </button>
                </div>
              )}
            </div>
          </div>

          {/* Tarjeta Perplexity */}
          <button
            onClick={() => setShowCharacterInfo(true)}
            style={{
              backgroundColor: "rgba(255, 255, 255, 0.1)",
              backdropFilter: "blur(10px)",
              borderRadius: "16px",
              padding: "24px",
              border: "1px solid rgba(255, 255, 255, 0.2)",
              cursor: "pointer",
              transition: "all 0.3s ease",
              textAlign: "left",
              color: "white",
            }}
            onMouseEnter={(e) => {
              e.currentTarget.style.backgroundColor = "rgba(255, 255, 255, 0.15)";
              e.currentTarget.style.transform = "translateY(-4px)";
              e.currentTarget.style.boxShadow = "0 8px 32px rgba(0, 0, 0, 0.3)";
            }}
            onMouseLeave={(e) => {
              e.currentTarget.style.backgroundColor = "rgba(255, 255, 255, 0.1)";
              e.currentTarget.style.transform = "translateY(0)";
              e.currentTarget.style.boxShadow = "none";
            }}
          >
            <div style={{ display: "flex", alignItems: "center", gap: "12px", marginBottom: "12px" }}>
              <div
                style={{
                  width: "48px",
                  height: "48px",
                  backgroundColor: "#6366f1",
                  borderRadius: "12px",
                  display: "flex",
                  alignItems: "center",
                  justifyContent: "center",
                  fontSize: "24px",
                }}
              >
                🧠
              </div>
              <div>
                <h3 style={{ fontSize: "18px", fontWeight: "bold", margin: 0 }}>
                  ¿Quieres saber más sobre
                </h3>
                <h3 style={{ fontSize: "18px", fontWeight: "bold", margin: 0 }}>
                  {characterName}?
                </h3>
              </div>
            </div>
            <p style={{ fontSize: "14px", color: "#cbd5e1", margin: "0 0 12px 0" }}>
              Pregunta a Perplexity
            </p>
            <div
              style={{
                backgroundColor: "#6366f1",
                color: "white",
                padding: "8px 16px",
                borderRadius: "8px",
                fontSize: "14px",
                fontWeight: "600",
                display: "inline-block",
              }}
            >
              Abrir Perplexity
            </div>
          </button>
        </div>

        {/* Botón de cerrar en la esquina */}
        <button
          onClick={onClose}
          style={{
            position: "absolute",
            top: "20px",
            right: "20px",
            backgroundColor: "rgba(255, 255, 255, 0.1)",
            border: "1px solid rgba(255, 255, 255, 0.2)",
            borderRadius: "50%",
            width: "40px",
            height: "40px",
            display: "flex",
            alignItems: "center",
            justifyContent: "center",
            cursor: "pointer",
            color: "white",
            fontSize: "18px",
            transition: "all 0.2s ease",
          }}
          onMouseEnter={(e) => {
            e.currentTarget.style.backgroundColor = "rgba(255, 255, 255, 0.2)";
          }}
          onMouseLeave={(e) => {
            e.currentTarget.style.backgroundColor = "rgba(255, 255, 255, 0.1)";
          }}
        >
          ✕
        </button>
      </div>

      {/* Modal Components */}
      <CharacterInfo
        characterName={characterName}
        isVisible={showCharacterInfo}
        onClose={() => setShowCharacterInfo(false)}
      />

      <MovistarInfo
        characterName={characterName}
        isVisible={showMovistarInfo}
        onClose={() => setShowMovistarInfo(false)}
      />
    </div>
    </>
  );
};
